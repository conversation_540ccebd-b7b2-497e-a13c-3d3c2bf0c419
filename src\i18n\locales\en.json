{"app": {"title": "<PERSON>", "subtitle": "Professional desktop application and toolkit for Claude CLI"}, "navigation": {"ccAgents": "CC Agents", "ccProjects": "CC Projects", "settings": "Settings", "usage": "Usage Dashboard", "mcpManager": "MCP Manager", "relayStations": "Relay Station Manager"}, "buttons": {"create": "Create", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "run": "Run", "stop": "Stop", "resume": "Resume", "export": "Export", "import": "Import", "browse": "Browse", "select": "Select", "confirm": "Confirm", "close": "Close", "back": "Back", "next": "Next", "finish": "Finish", "refresh": "Refresh", "search": "Search", "clear": "Clear", "copy": "Copy", "paste": "Paste", "upload": "Upload", "download": "Download"}, "agents": {"title": "CC Agents", "createAgent": "Create Agent", "agentName": "Agent Name", "agentDescription": "Agent Description", "systemPrompt": "System Prompt", "model": "Model", "icon": "Icon", "sandbox": "Sandbox Profile", "execution": "Execution", "runs": "Runs", "history": "History", "noAgents": "No agents created yet", "createFirst": "Create your first CC Agent to get started"}, "projects": {"title": "CC Projects", "projectName": "Project Name", "sessions": "Sessions", "lastModified": "Last Modified", "noProjects": "No projects found", "openProject": "Open Project", "newSession": "New Session"}, "settings": {"title": "Settings", "general": "General", "appearance": "Appearance", "language": "Language", "theme": "Theme", "themeLight": "Light", "themeDark": "Dark", "themeSystem": "System", "themeDescription": "Choose interface theme", "claudeBinary": "<PERSON>", "checkpoints": "Checkpoints", "storage": "Storage"}, "usage": {"title": "Usage Dashboard", "totalCost": "Total Cost", "tokensUsed": "Tokens Used", "requests": "Requests", "byModel": "By Model", "byProject": "By Project", "byDate": "By Date"}, "mcp": {"title": "MCP Manager", "servers": "Servers", "addServer": {"title": "Add MCP Server", "description": "Configure a new Model Context Protocol server", "serverName": "Server Name", "serverNamePlaceholder": "my-server", "serverNameDescription": "A unique name to identify this server", "command": "Command", "commandPlaceholder": "/path/to/server", "commandDescription": "The command to execute the server", "arguments": "Arguments (optional)", "argumentsPlaceholder": "arg1 arg2 arg3", "argumentsDescription": "Space-separated command arguments", "scope": "<PERSON><PERSON>", "scopeLocal": "Local (this project only)", "scopeProject": "Project (shared via .mcp.json)", "scopeUser": "User (all projects)", "url": "URL", "urlPlaceholder": "https://example.com/sse-endpoint", "urlDescription": "The SSE endpoint URL", "environmentVariables": "Environment Variables", "addVariable": "Add Variable", "keyPlaceholder": "KEY", "valuePlaceholder": "value", "envVarHelp": "Only enabled environment variables will be added to the server configuration. Click the toggle to enable/disable variables.", "enableEnvVar": "Enable environment variable", "disableEnvVar": "Disable environment variable", "addingServer": "Adding Server...", "addStdioServer": "Add Stdio Server", "addSseServer": "Add SSE Server", "exampleCommands": "Example Commands", "examplePostgres": "• Postgres: /path/to/postgres-mcp-server --connection-string \"postgresql://...\"", "exampleWeather": "• Weather API: /usr/local/bin/weather-cli --api-key ABC123", "exampleSse": "• SSE Server: https://api.example.com/mcp/stream", "serverNameRequired": "Server name is required", "commandRequired": "Command is required", "urlRequired": "URL is required", "failedToAddServer": "Failed to add server"}, "serverName": "Server Name", "serverUrl": "Server URL", "status": "Status", "connected": "Connected", "disconnected": "Disconnected", "importExport": {"title": "Import & Export", "description": "Import MCP servers from other sources or export your configuration", "importScope": "<PERSON><PERSON><PERSON>", "scopeDescription": "Choose where to save imported servers from JSON files", "importFromDesktop": "Import from <PERSON>", "importFromDesktopDescription": "Automatically imports all MCP servers from Claude Desktop. Installs to user scope (available across all projects).", "importing": "Importing...", "importFromJson": "Import from JSON", "importFromJsonDescription": "Import server configuration from a JSON file", "chooseJsonFile": "Choose JSON File", "exportConfiguration": "Export Configuration", "exportConfigurationDescription": "Export your MCP server configuration", "exportComingSoon": "Export (Coming Soon)", "useAsServer": "Use Claude Code as MCP Server", "useAsServerDescription": "Start Claude Code as an MCP server that other applications can connect to", "startMcpServer": "Start MCP Server", "jsonFormatExamples": "JSON Format Examples", "singleServer": "Single server:", "multipleServers": "Multiple servers (.mcp.json format):", "invalidJsonFile": "Invalid JSON file. Please check the format.", "unrecognizedJsonFormat": "Unrecognized JSON format. Expected MCP server configuration.", "failedToImportJson": "Failed to import JSON file", "exportComingSoonMessage": "Export functionality coming soon!", "mcpServerStarted": "Claude Code MCP server started. You can now connect to it from other applications.", "failedToStartMcpServer": "Failed to start Claude Code as MCP server", "enterServerName": "Enter a name for this server:"}, "serverList": {"title": "Configured Servers", "serversConfigured_one": "{{count}} server configured", "serversConfigured_other": "{{count}} servers configured", "refresh": "Refresh", "running": "Running", "showFull": "Show full", "hide": "<PERSON>de", "copy": "Copy", "copied": "Copied!", "command": "Command", "arguments": "Arguments", "url": "URL", "environmentVariables": "Environment Variables", "environmentVarsCount": "Environment variables: {{count}}", "noServers": "No MCP servers configured", "noServersDescription": "Add a server to get started with Model Context Protocol", "scopeLocal": "Local (Project-specific)", "scopeProject": "Project (Shared via .mcp.json)", "scopeUser": "User (All projects)"}}, "errors": {"generic": "An error occurred", "networkError": "Network error", "fileNotFound": "File not found", "permissionDenied": "Permission denied", "invalidInput": "Invalid input", "claudeNotFound": "Claude <PERSON> not found"}, "messages": {"success": "Success", "saved": "Saved successfully", "deleted": "Deleted successfully", "copied": "Copied to clipboard", "loading": "Loading...", "processing": "Processing...", "connecting": "Connecting...", "disconnecting": "Disconnecting..."}, "claude": {"title": "<PERSON>", "session": "Session", "sessions": "Sessions", "newSession": "New Session", "resumeSession": "Resume Session", "sessionHistory": "Session History", "prompt": "Prompt", "response": "Response", "thinking": "Thinking...", "generating": "Generating...", "completed": "Completed", "failed": "Failed", "cancelled": "Cancelled", "model": {"claude35Sonnet": "Claude 3.5 Sonnet", "claude35Haiku": "Claude 3.5 Haiku", "claude3Opus": "Claude 3 Opus"}, "status": {"title": "Claude CLI Status", "checking": "Checking...", "connected": "Connected", "disconnected": "Disconnected", "error": "Error", "unknown": "Unknown", "refresh": "Refresh status", "settings": "Claude settings", "connection": "Connection", "version": "Version", "lastChecked": "Last checked", "errorTitle": "Connection Error", "helpTitle": "Need help?", "helpText": "Install Claude CLI using: npm install -g @anthropic/claude"}}, "timeline": {"title": "Timeline", "checkpoint": "Checkpoint", "checkpoints": "Checkpoints", "createCheckpoint": "Create Checkpoint", "restoreCheckpoint": "Restore Checkpoint", "deleteCheckpoint": "Delete Checkpoint", "forkSession": "Fork Session", "diff": "Diff", "changes": "Changes", "noChanges": "No Changes"}, "files": {"title": "Files", "fileName": "File Name", "filePath": "File Path", "fileSize": "File Size", "lastModified": "Last Modified", "openFile": "Open File", "saveFile": "Save File", "deleteFile": "Delete File", "renameFile": "Rename File", "copyPath": "Copy Path", "claudeMd": "CLAUDE.md File", "editClaudeMd": "Edit CLAUDE.md", "preview": "Preview", "editor": "Editor", "markdownEditor": {"title": "CLAUDE.md", "description": "Edit your Claude Code system prompt", "saving": "Saving...", "save": "Save", "savedSuccessfully": "CLAUDE.md saved successfully", "failedToSave": "Failed to save CLAUDE.md", "failedToLoad": "Failed to load CLAUDE.md file", "unsavedChanges": "You have unsaved changes. Are you sure you want to leave?"}}, "sandbox": {"title": "Sandbox", "profile": "Profile", "profiles": "Profiles", "createProfile": "Create Profile", "permissions": "Permissions", "fileAccess": "File Access", "networkAccess": "Network Access", "systemAccess": "System Access", "violations": "Violations", "security": "Security", "restricted": "Restricted", "allowed": "Allowed", "denied": "Denied"}, "execution": {"title": "Execution", "status": "Status", "running": "Running", "completed": "Completed", "failed": "Failed", "cancelled": "Cancelled", "startTime": "Start Time", "endTime": "End Time", "duration": "Duration", "output": "Output", "logs": "Logs", "metrics": "Metrics", "performance": "Performance"}, "dialogs": {"confirmDelete": "Confirm Delete", "confirmDeleteMessage": "Are you sure you want to delete this item? This action cannot be undone.", "confirmCancel": "Confirm Cancel", "confirmCancelMessage": "Are you sure you want to cancel the current operation? Unsaved changes will be lost.", "selectFile": "Select File", "selectFolder": "Select Folder", "saveAs": "Save As", "openFile": "Open File"}, "tooltips": {"createAgent": "Create new CC Agent", "runAgent": "Run selected agent", "stopExecution": "Stop current execution", "exportAgent": "Export agent configuration", "importAgent": "Import agent configuration", "refreshList": "Refresh list", "openSettings": "Open settings", "viewUsage": "View usage statistics", "manageMCP": "Manage MCP servers"}, "placeholders": {"searchAgents": "Search agents...", "searchProjects": "Search projects...", "enterPrompt": "Enter your prompt...", "agentName": "Enter agent name", "agentDescription": "Describe agent functionality and purpose", "systemPrompt": "Define agent behavior and capabilities", "serverName": "Enter server name", "serverUrl": "Enter server URL"}, "common": {"backToHome": "← Back to Home", "newClaudeSession": "New Claude Code session", "browseClaudeSessions": "Browse your Claude Code sessions", "noProjectsFound": "No projects found in ~/.claude/projects", "ccProjectsTitle": "CC Projects", "loadingProjects": "Failed to load projects. Please ensure ~/.claude directory exists.", "loadingSessions": "Failed to load sessions for this project.", "claudeStillResponding": "<PERSON> is still responding. If you leave, <PERSON> will continue running in the background.\n\nYou can return to this session from the projects view.\n\nDo you want to continue?", "configureClaudePreferences": "Configure Claude Code preferences", "savingSettings": "Saving...", "saveSettings": "Save Settings", "about": "About", "basicInformation": "Basic Information", "agentNameRequired": "Agent name is required", "systemPromptRequired": "System prompt is required", "failedToCreateAgent": "Failed to create agent", "failedToUpdateAgent": "Failed to update agent", "unsavedChanges": "You have unsaved changes. Click Save to persist them.", "editAgent": "Edit CC Agent", "createAgent": "Create CC Agent", "updateAgent": "Update your Claude Code agent", "createNewAgent": "Create new Claude Code agent", "saving": "Saving...", "save": "Save", "agentName": "Agent Name", "agentIcon": "Agent <PERSON>", "model": "Model:", "claude4Sonnet": "Claude 4 Sonnet", "claude4Opus": "Claude 4 Opus", "fasterEfficient": "Faster, efficient for most tasks", "moreCapable": "More capable, suitable for complex tasks", "defaultTaskOptional": "Default Task (Optional)", "defaultTaskDescription": "Will be used as default task placeholder when executing the agent", "systemPrompt": "System Prompt", "defineAgentBehavior": "Define your CC agent's behavior and capabilities", "enterUrl": "Enter URL...", "preview": "Preview", "webPreview": "Web Preview", "searchTodos": "Search todos...", "restoreCheckpoint": "Restore to checkpoint \"{description}\"? Current state will be saved as a new checkpoint.", "beforeMajorRefactoring": "e.g., Before major refactoring", "selectTable": "Select Table", "searchInTable": "Search in table...", "sqlQuery": "SQL Query", "searchCommands": "Search commands...", "commandName": "e.g., review, fix-issue", "commandCategory": "e.g., frontend, backend", "commandDescription": "Brief description of what this command does", "commandPrompt": "Enter prompt content. Use $ARGUMENTS for dynamic values.", "pathToClaudeCli": "Enter Claude CLI executable path (e.g., C:\\path\\to\\claude.exe)", "bashExample": "e.g., <PERSON><PERSON>(npm run test:*)", "exitFullScreen": "Exit Fullscreen (ESC)", "enterFullScreen": "Enter Fullscreen", "invalidUrl": "Invalid URL", "goBack": "Go Back", "goForward": "Go Forward", "failedToLoadTimeline": "Failed to load timeline", "failedToCreateCheckpoint": "Failed to create checkpoint", "failedToRestoreCheckpoint": "Failed to restore checkpoint", "failedToCompareCheckpoints": "Failed to compare checkpoints", "current": "Current", "noPrompt": "No prompt", "loadingTimeline": "Loading timeline...", "noCheckpointsYet": "No checkpoints yet", "descriptionOptional": "Description (Optional)", "failedToLoadTables": "Failed to load tables", "failedToLoadTableData": "Failed to load table data", "failedToExecuteSQL": "Failed to execute SQL", "failedToResetDatabase": "Failed to reset database", "null": "null", "true": "true", "false": "false", "basicInformationSection": "Basic Information", "projectDeleted": "Project \"{name}\" deleted successfully", "projectDeleteFailed": "Failed to delete project: {error}", "selectProjectDirectory": "Select Project Directory", "selectDirectoryFailed": "Failed to select directory: {error}", "agentExecutionFailed": "Agent execution failed", "agentExecutionCancelled": "Agent execution cancelled", "userStoppedExecution": "User stopped execution", "agentStillRunning": "Agent is running. If you leave, the agent will continue running in the background. You can view running sessions in the CC Agents run sessions tab.\n\nDo you want to continue?", "execution": "Execution: {name}", "fullscreen": "Fullscreen", "projectPath": "Project Path", "selectOrEnterProjectPath": "Select or enter project path", "failedToLoadAgents": "Failed to load agents", "agentDeletedSuccessfully": "Agent deleted successfully", "failedToDeleteAgent": "Failed to delete agent", "agentCreatedSuccessfully": "Agent created successfully", "agentUpdatedSuccessfully": "Agent updated successfully", "failedToLoadAgentOutput": "Failed to load agent output", "agentExecutionCompleted": "Agent execution completed", "agentExecutionWasCancelled": "Agent execution was cancelled", "outputCopiedAsJsonl": "Output copied as JSONL", "outputCopiedAsMarkdown": "Output copied as <PERSON><PERSON>", "agentExecutionStopped": "Agent execution stopped", "executionStoppedByUser": "Execution stopped by user", "failedToStopExecution": "Failed to stop execution: {error}", "failedToParseMessage": "Failed to parse message", "pending": "Pending", "pleaseEnterValidPath": "Please enter a valid path", "customClaudeCliPathSet": "Custom Claude CLI path set successfully", "revertedToAutoDetection": "Reverted to auto-detection", "failedToSetCustomPath": "Failed to set custom path", "failedToClearCustomPath": "Failed to clear custom path", "failedToLoadSettings": "Failed to load settings. Please ensure ~/.claude directory exists.", "settingsSavedSuccessfully": "Setting<PERSON> saved successfully!", "failedToSaveSettings": "Failed to save settings", "cancelSetCustomPath": "Cancel / Set Custom Path", "currentPath": "Current path", "notDetected": "Not detected", "key": "KEY", "claudeCodeInstallation": "Claude Code Installation", "customClaudeCliPath": "Custom Claude CLI Path", "permissionRules": "Permission Rules", "allowRules": "Allow Rules", "denyRules": "Deny <PERSON>", "bashAllowDescription": "Allow all bash commands", "environmentVariables": "Environment Variables", "advancedSettings": "Advanced Settings", "apiKeyHelperScript": "API Key Helper <PERSON>", "userHooks": "User Hooks", "cancel": "Cancel", "setCustomPath": "Set Custom Path", "task": "Task", "showSystemInitialization": "Show System Initialization Info", "showSystemInitializationDescription": "Display Session ID, Model, working directory and available tools info at session start", "includeCoAuthoredBy": "Include \"Co-authored by <PERSON>\"", "includeCoAuthoredByDescription": "Add Claude signature to git commits and pull requests", "verboseOutput": "Verbose Output", "verboseOutputDescription": "Show full bash and command output", "chatRetentionDays": "Chat Retention Days", "claudeBinaryPathChanged": "Claude binary path has been changed. Remember to save your settings.", "customClaudeCliPathSetSuccessfully": "Custom Claude CLI path set successfully", "setPath": "Set Path", "revertToAutoDetection": "Revert to Auto-detection", "customPathValidationDescription": "The custom path will be validated before saving. Make sure the file exists and is a valid Claude CLI executable.", "permissions": "Permissions", "environment": "Environment", "advanced": "Advanced", "hooks": "<PERSON>s", "commands": "Commands", "provider": "Provider", "addRule": "Add Rule", "noDenyRulesConfigured": "No deny rules configured.", "bashExampleDeny": "e.g., <PERSON><PERSON>(curl:*)", "examples": "Examples", "bashAllowAll": "Allow all bash commands", "bashAllowExact": "Allow exact command", "bashAllowPrefix": "Allow commands with prefix", "readSpecificFile": "Allow reading specific file", "editDocsDirectory": "Allow editing files in docs directory", "environmentVariablesDescription": "Environment variables applied to every Claude Code session", "addVariable": "Add Variable", "noEnvironmentVariablesConfigured": "No environment variables configured.", "environmentVariableToggleDescription": "Use switches to enable or disable environment variables. Only enabled variables will be applied to Claude Code sessions.", "enableEnvironmentVariable": "Enable environment variable", "disableEnvironmentVariable": "Disable environment variable", "value": "value", "commonVariables": "Common variables:", "enableDisableTelemetry": "Enable/disable telemetry (0 or 1)", "customModelName": "Custom model name", "disableCostWarnings": "Disable cost warnings (1)", "advancedSettingsDescription": "Additional configuration options for advanced users", "apiKeyHelperScriptPath": "/path/to/generate_api_key.sh", "apiKeyHelperDescription": "Custom script to generate auth values for API requests", "rawSettingsJson": "Raw Settings (JSON)", "rawSettingsDescription": "This shows the raw JSON that will be saved to ~/.claude/settings.json", "providerConfigApplied": "Provider configuration applied", "apiAddress": "API Address", "authToken": "<PERSON><PERSON>", "apiKey": "API Key", "providerConfigNote": "Provider configuration has been saved to the env section of Raw Settings, Claude instances will automatically use these settings", "noProviderConfigApplied": "No provider configuration applied", "useProviderTabMessage": "Use the \"Provider\" tab to configure API provider settings", "userHooksDescription": "Configure hooks that apply to all Claude Code sessions for your user account. These are stored in", "userHooksStorageLocation": "~/.claude/settings.json", "loadingExecutionDetails": "Failed to load execution details", "runNotFound": "Run record not found", "executionHistory": "Execution History", "stop": "Stop", "copyOutput": "Copy Output", "copyAsJsonl": "<PERSON><PERSON> as JSONL", "copyAsMarkdown": "<PERSON><PERSON> as <PERSON><PERSON>", "systemInitialization": "## System Initialization\n\n", "sessionId": "Session ID", "workingDirectory": "Working Directory", "tools": "Tools", "assistant": "Assistant", "tool": "Tool", "tokensInputOutput": "Tokens: {{input}} input, {{output}} output", "user": "User", "toolResult": "<PERSON><PERSON> Result", "executionResult": "Execution Result", "error": "Error", "userStoppedExecutionMessage": "User stopped execution", "status": "Status", "tokens": "tokens", "cost": "Cost", "date": "**Date:**", "checkpointSettings": "Checkpoint Settings", "close": "Close", "experimentalFeature": "Experimental Feature", "checkpointingCaution": "Checkpointing may affect directory structure or cause data loss. Use with caution.", "automaticCheckpoints": "Automatic Checkpoints", "automaticCheckpointsDescription": "Automatically create checkpoints based on the selected strategy", "checkpointStrategy": "Checkpoint Strategy", "manualOnly": "Manual Only", "perPrompt": "Per Prompt", "perToolUse": "Per Tool Use", "smartRecommended": "Smart (Recommended)", "manualOnlyDescription": "Checkpoints will only be created manually", "perPromptDescription": "A checkpoint will be created after each user prompt", "perToolUseDescription": "A checkpoint will be created after each tool use", "smartDescription": "Checkpoints will be created after destructive operations", "storageManagement": "Storage Management", "totalCheckpoints": "Total checkpoints", "keepRecentCheckpoints": "Keep Recent Checkpoints", "cleanUp": "Clean Up", "removeOldCheckpoints": "Remove old checkpoints, keeping only the most recent {count}", "loadCheckpointSettingsFailed": "Failed to load checkpoint settings", "settingSavedSuccessfully": "Setting<PERSON> saved successfully", "saveCheckpointSettingsFailed": "Failed to save checkpoint settings", "deletedOldCheckpoints": "Deleted {count} old checkpoints", "cleanupCheckpointsFailed": "Failed to cleanup checkpoints", "usageDashboard": "Usage Dashboard", "trackUsageAndCosts": "Track your Claude Code usage and costs", "allTime": "All Time", "last7Days": "7 Days", "last30Days": "30 Days", "today": "Today", "loadingUsageStats": "Loading usage statistics...", "loadUsageStatsFailed": "Failed to load usage statistics. Please try again.", "retry": "Retry", "totalCost": "Total Cost", "totalSessions": "Total Sessions", "totalTokens": "Total Tokens", "averagePerSessionCost": "Average Per Session Cost", "overview": "Overview", "byModel": "By Model", "byProject": "By Project", "bySessions": "By Sessions", "byApiBaseUrl": "By API Address", "timeline": "Timeline", "tokenBreakdown": "Token Breakdown", "inputTokens": "Input Tokens", "outputTokens": "Output Tokens", "cacheWrites": "<PERSON><PERSON>", "cacheReads": "<PERSON><PERSON>", "mostUsedModels": "Most Used Models", "topProjects": "Top Projects", "sessions": " sessions", "usageByModel": "Usage by Model", "input": "Input: ", "output": "Output: ", "cacheWrite": "<PERSON><PERSON> Write: ", "cacheRead": "<PERSON><PERSON> Read: ", "usageByProject": "Usage by Project", "perSession": "/session", "usageBySessions": "Usage by Sessions", "usageByApiBaseUrl": "Usage by API Address", "dailyUsage": "Daily Usage", "costLabel": "Cost:", "modelsUsed": " models", "dailyUsageOverTime": "Daily usage over time", "noUsageDataForPeriod": "No usage data for the selected period", "usingTool": "Using tool:", "editResult": "Edit Result", "multiEditResult": "MultiEdit Result", "directoryContents": "Directory Contents", "readResult": "Read Result", "toolDidNotReturnOutput": "Tool did not return any output", "executionFailed": "Execution Failed", "errorRenderingMessage": "Error rendering message", "unknownError": "Unknown error", "tokensLabel": "Tokens:", "totalTokensStats": "Total tokens: {{total}} ({{input}} in, {{output}} out)", "durationLabel": "Duration:", "turnsLabel": "Turns:", "ccAgentsTitle": "CC Agents", "manageCCAgents": "Manage your Claude Code agents", "import": "Import", "fromFile": "From File", "fromGitHub": "From GitHub", "createCCAgent": "Create CC Agent", "noAgentsYet": "No agents yet", "createFirstAgent": "Create your first CC Agent to get started", "createdLabel": "Created: ", "executeAgent": "Execute", "executeAgentTitle": "Execute agent", "editAgentTitle": "Edit agent", "exportAgentTitle": "Export agent to .claudia.json", "deleteAgent": "Delete", "deleteAgentTitle": "Delete agent", "previous": "Previous", "next": "Next", "pageOf": "Page {current} of {total}", "recentExecutions": "Recent Executions", "deleteAgentDialog": "Delete Agent", "deleteAgentConfirm": "Are you sure you want to delete the agent \"{name}\"? This action cannot be undone and will permanently remove the agent and all its associated data.", "deleting": "Deleting...", "deleteAgentButton": "Delete Agent", "agentExportSuccess": "Agent \"{name}\" exported successfully", "agentExportFailed": "Failed to export agent", "agentImportSuccess": "Agent imported successfully", "agentImportFailed": "Failed to import agent", "importFromGitHubSuccess": "Successfully imported agent from GitHub", "providerName": "Provider Name", "providerDescription": "Description", "authenticationInfo": "Authentication Info", "atLeastOneRequired": "(at least one required)", "modelName": "Model Name", "someProvidersRequireModel": "Some providers require specific model names", "addingConfig": "Adding...", "updatingConfig": "Updating...", "addConfig": "Add Configuration", "updateConfig": "Update Configuration", "pleaseEnterProviderName": "Please enter provider name", "pleaseEnterApiAddress": "Please enter API address", "apiAddressMustStartWith": "API address must start with http:// or https://", "pleaseEnterAuthTokenOrApiKey": "Please fill in at least one of Auth Token or API Key", "addOrUpdateProviderFailed": "Failed to {action} provider configuration: {error}", "updating": "update", "adding": "add", "providerManager": "Provider Manager", "switchClaudeApiProviders": "One-click switching between different Claude API providers", "addProvider": "Add Provider", "viewCurrentConfig": "View Current Config", "clearConfig": "Clear Config", "clearing": "Clearing...", "noProvidersConfigured": "No providers configured yet", "addFirstProvider": "Add your first provider", "currentlyUsed": "Currently Used", "description": "Description: ", "apiAddressLabel": "API Address: ", "authTokenLabel": "Auth <PERSON>ken: ", "apiKeyLabel": "API Key: ", "modelLabel": "- Model:", "testConnection": "Test Connection", "editProvider": "Edit Provider", "deleteProvider": "Delete Provider", "selected": "Selected", "switchToConfig": "Switch to this config", "show": "Show", "hide": "<PERSON>de", "currentEnvironmentConfig": "Current Environment Variables Config", "noAnthropicEnvVars": "No ANTHROPIC environment variables detected", "editProviderDialog": "Edit Provider", "addProviderDialog": "Add Provider", "loadProviderConfigFailed": "Failed to load provider configuration", "switchProviderFailed": "Failed to switch provider", "clearConfigFailed": "Failed to clear configuration", "connectionTestFailed": "Connection test failed", "confirmDeleteProvider": "Are you sure you want to delete provider \"{name}\"?", "providerDeleteSuccess": "Provider deleted successfully", "deleteProviderFailed": "Failed to delete provider", "providerUpdateSuccess": "Provider updated successfully", "providerAddSuccess": "Provider added successfully", "updateProviderFailed": "Failed to update provider", "addProviderFailed": "Failed to add provider", "loadingProviderConfig": "Loading provider configuration...", "basicInfo": "Basic Information", "providerNameRequired": "Provider Name *", "providerNamePlaceholder": "e.g., OpenAI Official", "descriptionPlaceholder": "e.g., OpenAI Official API", "apiAddressRequired": "API Address *", "apiAddressPlaceholder": "https://api.anthropic.com", "authTokenPlaceholder": "sk-ant-...", "apiKeyPlaceholder": "sk-...", "modelNamePlaceholder": "claude-3-5-sonnet-20241022 (optional)", "hooksConfiguration": "Hooks Configuration", "preToolUse": "Pre Tool Use", "preToolUseDescription": "Run before tool calls, can block and provide feedback", "postToolUse": "Post Tool Use", "postToolUseDescription": "Run after tools complete successfully", "notification": "Notification", "notificationDescription": "Custom notifications when <PERSON> needs attention", "stopDescription": "Run when <PERSON> completes response", "subagentStop": "Subagent Stop", "subagentStopDescription": "Run when Claude subagents (tasks) complete", "loadingHooksConfig": "Loading hooks configuration...", "projectScope": "Project Scope", "localScope": "Local Scope", "userScope": "User <PERSON>", "templates": "Templates", "configureShellCommands": "Configure shell commands to execute at various points in Claude Code's lifecycle.", "localSettingsNotCommitted": "These settings are not committed to version control.", "validationErrors": "Validation Errors:", "securityWarnings": "Security Warnings:", "addCommand": "Add Command", "noCommandsAdded": "No commands added yet", "enterCommandPlaceholder": "Enter command line command...", "enterShellCommandPlaceholder": "Enter shell command...", "secondsLabel": "seconds", "pattern": "Pattern", "toolNamePattern": "Tool name pattern (supports regex). Leave empty to match all tools.", "patternPlaceholder": "e.g., <PERSON><PERSON>, <PERSON>|Write, mcp__.*", "customPattern": "Custom", "commonPatterns": "Common patterns", "noHooksConfigured": "No hooks configured for this event", "addHook": "Add Hook", "addAnother": "Add Another", "matcher": "Matcher", "command": "Command", "hookTemplates": "Hook Templates", "chooseTemplate": "Choose a pre-configured hook template to get started quickly", "matcherLabel": "Matcher:", "selectClaudeInstallation": "Select Claude Code Installation", "searchingInstallations": "Searching for Claude installations...", "multipleInstallationsFound": "Multiple Claude Code installations were found on your system. Please select which one you'd like to use.", "claudeNotFoundInCommonLocations": "Claude Code was not found in any of the common installation locations. Please install Claude Code to continue.", "searchedLocations": "Searched locations:", "installTip": "You can install Claude Code using", "installationGuide": "Installation Guide", "validating": "Validating...", "saveSelection": "Save Selection", "noInstallationFound": "No Installation Found", "pleaseSelectClaudeInstallation": "Please select a Claude installation", "claudeCodeInstallationSelector": "Claude Code Installation", "loadingAvailableInstallations": "Loading available installations...", "errorLoadingInstallations": "Error loading installations", "availableInstallations": "Available Installations", "selectClaudeInstallationPlaceholder": "Select Claude installation", "bundled": "Bundled", "claudeCodeBundled": "<PERSON> (Bundled)", "versionUnknown": "Version unknown", "recommended": "Recommended", "systemInstallations": "System Installations", "system": "System", "customInstallations": "Custom Installations", "custom": "Custom", "selectedInstallation": "Selected Installation", "path": "Path:", "source": "Source:", "version": "Version:", "chooseBestCompatibility": "Choose your preferred Claude Code installation. Bundled version is recommended for best compatibility.", "savingSelection": "Saving...", "projectDirectory": "Project Directory", "projectDirectoryPlaceholder": "/path/to/your/project", "projectNotSelected": "No project selected", "backToSessionList": "Back to Session List", "claudeCodeSession": "Claude <PERSON> Session", "timelineNavigator": "Timeline Navigator", "sessionTimeline": "Session Timeline", "queuedPrompts": "Queued Prompts ({{count}})", "scrollToTop": "Scroll to top", "scrollToBottom": "Scroll to bottom", "forkSession": "Fork Session", "createSessionBranch": "Create a new session branch from the selected checkpoint.", "newSessionName": "New Session Name", "newSessionNamePlaceholder": "e.g., Alternative approach", "createFork": "Create Fork", "slashCommands": "Slash Commands", "manageProjectSlashCommands": "Manage project-specific slash commands for {projectPath}", "pleaseSelectProjectDirectory": "Please select a project directory first", "sendPromptFailed": "Failed to send prompt", "loadingSessionHistory": "加载会话历史记录...", "failedToLoadSessionHistory": "Failed to load session history", "initializingClaudeCode": "Initializing Claude Code...", "claudeCodeMarkdownSession": "# Claude Code Session\n\n", "project": "Project", "sessionIdLabel": "- Session ID:", "workingDirectoryLabel": "- Working Directory:", "toolsLabel": "- Tools:", "assistantLabel": "## Assistant\n\n", "toolLabel": "### Tool:", "tokensMarkdown": "*Tokens: {input} in, {output} out*\n\n", "userLabel": "## User\n\n", "toolResultLabel": "### <PERSON><PERSON> Result\n\n", "executionResultLabel": "## Execution Result\n\n", "errorLabel": "**Error:**", "sessionCancelledByUser": "Session cancelled by user", "failedToCancelExecution": "Failed to cancel execution: {error}. The process may still be running in the background.", "forkToNewSession": "Fork-", "failedToForkCheckpoint": "Failed to fork checkpoint", "forkedToNewSession": "Forked to new session:", "loadCommandsFailed": "Failed to load commands", "saveCommandFailed": "Failed to save command", "deleteCommandFailed": "Failed to delete command", "projectSlashCommands": "Project Slash Commands", "createCustomCommandsForProject": "Create custom commands for this project", "createCustomCommandsWorkflow": "Create custom commands to simplify your workflow", "newCommand": "New Command", "allCommands": "All Commands", "arguments": "Arguments", "toolsCount": "{{count}} tools", "script": "<PERSON><PERSON><PERSON>", "file": "File", "hideContent": "Hide Content", "showContent": "Show Content", "commandsNotFound": "No commands found", "noProjectCommandsYet": "No project commands created yet", "noCommandsYet": "No commands created yet", "createFirstProjectCommand": "Create your first project command", "createFirstCommand": "Create your first command", "editCommand": "Edit Command", "createNewCommand": "Create New Command", "scope": "<PERSON><PERSON>", "userGlobal": "User (Global)", "availableInAllProjects": "Available in all projects", "availableOnlyInThisProject": "Available only in this project", "commandNameRequired": "Command Name*", "commandNamespace": "Namespace (Optional)", "commandContentRequired": "Command Content*", "argumentsUsage": "Use <code>$ARGUMENTS</code> for user input, <code>@filename</code> for files, <code>!`command`</code> for bash commands", "allowedTools": "Allowed Tools", "selectToolsForClaude": "Select tools <PERSON> can use with this command", "argumentsPlaceholder": "[arguments]", "deleteCommand": "Delete Command", "confirmDeleteCommand": "Are you sure you want to delete this command?", "actionCannotBeUndone": "This action cannot be undone. The command file will be permanently deleted.", "projectCommands": "Project Commands", "userCommands": "User Commands", "databaseStorage": "Database Storage", "resetDb": "Reset DB", "rowsLabel": "rows", "addNewRow": "Add Row", "actions": "Actions", "showingRows": "Showing {start} to {end} of {total} rows", "editRow": "Edit Row", "updateRowValues": "Update the values for this row in the {table} table.", "primaryKey": "(Primary Key)", "type": "Type:", "notNull": ", NOT NULL", "default": ", Default:", "update": "Update", "newRow": "New Row", "addNewRowToTable": "Add a new row to the {table} table.", "required": "(Required)", "insert": "Insert", "deleteRow": "Delete Row", "confirmDeleteRow": "Are you sure you want to delete this row? This action cannot be undone.", "resetDatabase": "Reset Database", "resetDatabaseWarning": "This will delete all data and recreate the database with its default structure (empty tables for agents, agent_runs, and app_settings). The database will be restored to the same state as when you first installed the app. This action cannot be undone.", "allDataWillBeDeleted": "All your agents, runs, and settings will be permanently deleted!", "sqlQueryEditor": "SQL Query Editor", "executeSqlCaution": "Execute raw SQL queries on the database. Use with caution.", "sqlQueryLabel": "SQL Query", "queryExecutedSuccessfully": "Query executed successfully. {affected} rows affected.", "lastInsertId": "Last insert ID: {id}", "execute": "Execute", "databaseResetComplete": "Database Reset Complete: The database has been restored to its default state with empty tables (agents, agent_runs, app_settings).", "updateRowFailed": "Failed to update row", "deleteRowFailed": "Failed to delete row", "insertRowFailed": "Failed to insert row", "mcpServers": "MCP Servers", "manageMcpServers": "Manage Model Context Protocol servers", "loadMcpServersFailed": "Failed to load MCP servers. Please ensure Claude Code is installed.", "mcpServerAddedSuccess": "MCP server added successfully!", "serverDeletedSuccess": "Server \"{name}\" deleted successfully!", "serversImportedSuccess": "Successfully imported {imported} servers!", "serversImportedWithFailures": "Imported {imported} servers, {failed} failed", "servers": "Servers", "addServer": "Add Server", "importExport": "Import/Export", "composePrompt": "Compose your prompt", "attachmentPreview": "Attachment Preview", "screenshotPreview": "Screenshot preview", "promptInputPlaceholder": "Enter your prompt here...", "thinking": "Thinking:", "dropImagesHere": "Drop images here...", "askClaudeAnything": "Ask <PERSON> anything...", "keyboardShortcuts": "Press Enter to send, Shift+Enter for new line, or paste images", "keyboardShortcutsWithFiles": "Press Enter to send, Shift+Enter for new line, @ to mention files, / for commands, or paste images", "saveClipboardImageFailed": "Failed to save clipboard image, please try again", "pasteImageFailed": "Failed to paste image, please try again", "letClaudeDecide": "Let <PERSON> decide", "basicReasoning": "Basic reasoning", "deeperThinking": "Deeper analysis", "extensiveReasoning": "Extensive reasoning", "maximumComputation": "Maximum computation", "moreCapableBetter": "More capable, better for complex tasks"}, "relayStations": {"title": "Relay Station Manager", "description": "Manage your Claude API relay stations", "addStation": "Add Station", "stationList": "Station List", "noStations": "No stations available", "noStationsDescription": "Click \"Add Station\" to start configuring", "stationName": "Station Name", "stationDescription": "Description", "apiUrl": "API URL", "adapterType": "Adapter Type", "authMethod": "Authentication Method", "systemToken": "System Token", "enabled": "Enabled", "disabled": "Disabled", "status": "Status", "details": "Details", "tokens": "Tokens", "logs": "Logs", "delete": "Delete", "edit": "Edit", "testConnection": "Test Connection", "connectionSuccess": "Connection successful", "connectionFailed": "Connection failed", "responseTime": "Response time", "stationInfo": "Station Info", "version": "Version", "announcement": "Announcement", "tokenManagement": "Token Management", "tokenList": "Token List", "userInfo": "User Info", "userId": "User ID", "username": "Username", "email": "Email", "balance": "Balance", "used": "Used", "requestCount": "Request Count", "noTokens": "No tokens available", "selectTokenForUserInfo": "Select a token to view user info", "cannotGetUserInfo": "Cannot get user info", "cannotGetStationInfo": "Cannot get station info", "noLogs": "No logs available", "selectStationForLogs": "Select a station to view logs", "confirmDelete": "Are you sure you want to delete station \"{name}\"?", "addStationSuccess": "Station added successfully", "updateStationSuccess": "Station updated successfully", "deleteStationSuccess": "Station deleted successfully", "fetchStationsFailed": "Failed to fetch station list", "deleteStationFailed": "Failed to delete station", "addStationDialog": {"title": "Add Station", "description": "Add a new relay station configuration"}, "stationDetailsDialog": {"title": "Station Details", "description": "Station details and status information"}, "tokenManagementDialog": {"title": "Token Management", "description": "Manage access tokens and user information for the station"}, "adapters": {"newapi": "NewAPI", "oneapi": "OneAPI", "yourapi": "YourAPI", "custom": "Custom"}, "authMethods": {"bearerToken": "<PERSON><PERSON>", "apiKey": "API Key", "custom": "Custom"}}}
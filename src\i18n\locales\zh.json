{"app": {"title": "<PERSON>", "subtitle": "专业的 Claude CLI 桌面应用和开发工具包"}, "navigation": {"ccAgents": "CC 智能体", "ccProjects": "CC 项目", "settings": "设置", "usage": "使用统计", "mcpManager": "MCP 管理器", "relayStations": "中转站管理"}, "buttons": {"create": "创建", "save": "保存", "cancel": "取消", "delete": "删除", "edit": "编辑", "run": "运行", "stop": "停止", "resume": "恢复", "export": "导出", "import": "导入", "browse": "浏览", "select": "选择", "confirm": "确认", "close": "关闭", "back": "返回", "next": "下一步", "finish": "完成", "refresh": "刷新", "search": "搜索", "clear": "清除", "copy": "复制", "paste": "粘贴", "upload": "上传", "download": "下载"}, "agents": {"title": "CC 智能体", "createAgent": "创建智能体", "agentName": "智能体名称", "agentDescription": "智能体描述", "systemPrompt": "系统提示词", "model": "模型", "icon": "图标", "sandbox": "沙箱配置", "execution": "执行", "runs": "运行记录", "history": "历史记录", "noAgents": "尚未创建任何智能体", "createFirst": "创建您的第一个 CC 智能体开始使用"}, "projects": {"title": "CC 项目", "projectName": "项目名称", "sessions": "会话", "lastModified": "最后修改", "noProjects": "未找到项目", "openProject": "打开项目", "newSession": "新建会话"}, "settings": {"title": "设置", "general": "常规", "appearance": "外观", "language": "语言", "theme": "主题", "themeLight": "浅色", "themeDark": "深色", "themeSystem": "系统", "themeDescription": "选择界面主题", "claudeBinary": "<PERSON> 二进制文件", "checkpoints": "检查点", "storage": "存储"}, "usage": {"title": "使用统计", "totalCost": "总费用", "tokensUsed": "已使用令牌", "requests": "请求次数", "byModel": "按模型", "byProject": "按项目", "byDate": "按日期"}, "mcp": {"title": "MCP 管理器", "servers": "服务器", "addServer": {"title": "添加 MCP 服务器", "description": "配置一个新的模型上下文协议服务器", "serverName": "服务器名称", "serverNamePlaceholder": "my-server", "serverNameDescription": "用于标识此服务器的唯一名称", "command": "命令", "commandPlaceholder": "/path/to/server", "commandDescription": "执行服务器的命令", "arguments": "参数（可选）", "argumentsPlaceholder": "arg1 arg2 arg3", "argumentsDescription": "空格分隔的命令参数", "scope": "作用域", "scopeLocal": "本地（仅此项目）", "scopeProject": "项目（通过 .mcp.json 共享）", "scopeUser": "用户（所有项目）", "url": "URL", "urlPlaceholder": "https://example.com/sse-endpoint", "urlDescription": "SSE 端点 URL", "environmentVariables": "环境变量", "addVariable": "添加变量", "keyPlaceholder": "KEY", "valuePlaceholder": "value", "envVarHelp": "只有启用的环境变量会被添加到服务器配置中。点击切换按钮可启用/禁用变量。", "enableEnvVar": "启用环境变量", "disableEnvVar": "禁用环境变量", "addingServer": "正在添加服务器...", "addStdioServer": "添加 Stdio 服务器", "addSseServer": "添加 SSE 服务器", "exampleCommands": "示例命令", "examplePostgres": "• Postgres: /path/to/postgres-mcp-server --connection-string \"postgresql://...\"", "exampleWeather": "• Weather API: /usr/local/bin/weather-cli --api-key ABC123", "exampleSse": "• SSE Server: https://api.example.com/mcp/stream", "serverNameRequired": "服务器名称为必填项", "commandRequired": "命令为必填项", "urlRequired": "URL 为必填项", "failedToAddServer": "添加服务器失败"}, "serverName": "服务器名称", "serverUrl": "服务器地址", "status": "状态", "connected": "已连接", "disconnected": "已断开", "importExport": {"title": "导入导出", "description": "从其他来源导入 MCP 服务器或导出您的配置", "importScope": "导入范围", "scopeDescription": "选择从 JSON 文件导入的服务器保存位置", "importFromDesktop": "从 Claude Desktop 导入", "importFromDesktopDescription": "自动导入 Claude Desktop 中的所有 MCP 服务器。安装到用户范围（所有项目可用）。", "importing": "正在导入...", "importFromJson": "从 JSON 导入", "importFromJsonDescription": "从 JSON 文件导入服务器配置", "chooseJsonFile": "选择 JSON 文件", "exportConfiguration": "导出配置", "exportConfigurationDescription": "导出您的 MCP 服务器配置", "exportComingSoon": "导出（即将推出）", "useAsServer": "将 Claude Code 用作 MCP 服务器", "useAsServerDescription": "启动 Claude Code 作为其他应用程序可以连接的 MCP 服务器", "startMcpServer": "启动 MCP 服务器", "jsonFormatExamples": "JSON 格式示例", "singleServer": "单个服务器：", "multipleServers": "多个服务器（.mcp.json 格式）：", "invalidJsonFile": "无效的 JSON 文件。请检查格式。", "unrecognizedJsonFormat": "无法识别的 JSON 格式。期望 MCP 服务器配置。", "failedToImportJson": "导入 JSON 文件失败", "exportComingSoonMessage": "导出功能即将推出！", "mcpServerStarted": "Claude Code MCP 服务器已启动。您现在可以从其他应用程序连接到它。", "failedToStartMcpServer": "启动 Claude Code 作为 MCP 服务器失败", "enterServerName": "为此服务器输入名称："}, "serverList": {"title": "已配置的服务器", "serversConfigured": "已配置 {{count}} 个服务器", "refresh": "刷新", "running": "运行中", "showFull": "显示完整", "hide": "隐藏", "copy": "复制", "copied": "已复制！", "command": "命令", "arguments": "参数", "url": "URL", "environmentVariables": "环境变量", "environmentVarsCount": "环境变量：{{count}}", "noServers": "未配置 MCP 服务器", "noServersDescription": "添加服务器以开始使用模型上下文协议", "scopeLocal": "本地（项目特定）", "scopeProject": "项目（通过 .mcp.json 共享）", "scopeUser": "用户（所有项目）"}}, "errors": {"generic": "发生错误", "networkError": "网络错误", "fileNotFound": "文件未找到", "permissionDenied": "权限被拒绝", "invalidInput": "输入无效", "claudeNotFound": "未找到 Claude Code"}, "messages": {"success": "成功", "saved": "保存成功", "deleted": "删除成功", "copied": "已复制到剪贴板", "loading": "加载中...", "processing": "处理中...", "connecting": "连接中...", "disconnecting": "断开连接中..."}, "claude": {"title": "<PERSON>", "session": "会话", "sessions": "会话列表", "newSession": "新建会话", "resumeSession": "恢复会话", "sessionHistory": "会话历史", "prompt": "提示词", "response": "响应", "thinking": "思考中...", "generating": "生成中...", "completed": "已完成", "failed": "失败", "cancelled": "已取消", "model": {"claude35Sonnet": "Claude 3.5 Sonnet", "claude35Haiku": "Claude 3.5 Haiku", "claude3Opus": "Claude 3 Opus"}, "status": {"title": "Claude CLI 状态", "checking": "检查中...", "connected": "已连接", "disconnected": "已断开", "error": "错误", "unknown": "未知", "refresh": "刷新状态", "settings": "<PERSON> 设置", "connection": "连接", "version": "版本", "lastChecked": "最后检查", "errorTitle": "连接错误", "helpTitle": "需要帮助？", "helpText": "使用以下命令安装 Claude CLI：npm install -g @anthropic/claude"}}, "timeline": {"title": "时间线", "checkpoint": "检查点", "checkpoints": "检查点列表", "createCheckpoint": "创建检查点", "restoreCheckpoint": "恢复检查点", "deleteCheckpoint": "删除检查点", "forkSession": "分叉会话", "diff": "差异对比", "changes": "变更", "noChanges": "无变更"}, "files": {"title": "文件", "fileName": "文件名", "filePath": "文件路径", "fileSize": "文件大小", "lastModified": "最后修改时间", "openFile": "打开文件", "saveFile": "保存文件", "deleteFile": "删除文件", "renameFile": "重命名文件", "copyPath": "复制路径", "claudeMd": "CLAUDE.md 文件", "editClaudeMd": "编辑 CLAUDE.md", "preview": "预览", "editor": "编辑器", "markdownEditor": {"title": "CLAUDE.md", "description": "编辑您的 Claude Code 系统提示词", "saving": "保存中...", "save": "保存", "savedSuccessfully": "CLAUDE.md 保存成功", "failedToSave": "保存 CLAUDE.md 失败", "failedToLoad": "加载 CLAUDE.md 文件失败", "unsavedChanges": "您有未保存的更改。确定要离开吗？"}}, "sandbox": {"title": "沙箱", "profile": "配置文件", "profiles": "配置文件列表", "createProfile": "创建配置文件", "permissions": "权限", "fileAccess": "文件访问", "networkAccess": "网络访问", "systemAccess": "系统访问", "violations": "违规记录", "security": "安全", "restricted": "受限", "allowed": "允许", "denied": "拒绝"}, "execution": {"title": "执行", "status": "状态", "running": "运行中", "completed": "已完成", "failed": "失败", "cancelled": "已取消", "startTime": "开始时间", "endTime": "结束时间", "duration": "持续时间", "output": "输出", "logs": "日志", "metrics": "指标", "performance": "性能"}, "dialogs": {"confirmDelete": "确认删除", "confirmDeleteMessage": "您确定要删除此项目吗？此操作无法撤销。", "confirmCancel": "确认取消", "confirmCancelMessage": "您确定要取消当前操作吗？未保存的更改将丢失。", "selectFile": "选择文件", "selectFolder": "选择文件夹", "saveAs": "另存为", "openFile": "打开文件"}, "tooltips": {"createAgent": "创建新的 CC 智能体", "runAgent": "运行选定的智能体", "stopExecution": "停止当前执行", "exportAgent": "导出智能体配置", "importAgent": "导入智能体配置", "refreshList": "刷新列表", "openSettings": "打开设置", "viewUsage": "查看使用统计", "manageMCP": "管理 MCP 服务器"}, "placeholders": {"searchAgents": "搜索智能体...", "searchProjects": "搜索项目...", "enterPrompt": "输入您的提示词...", "agentName": "输入智能体名称", "agentDescription": "描述智能体的功能和用途", "systemPrompt": "定义智能体的行为和能力", "serverName": "输入服务器名称", "serverUrl": "输入服务器地址"}, "common": {"environmentVariables": "环境变量", "permissionRules": "权限规则", "allowRules": "允许的规则", "denyRules": "拒绝的规则", "noAllowRulesConfigured": "未配置允许规则。Claude 将为所有工具请求批准。", "permissionRulesDescription": "控制Claude Code可以在无需人工批准的情况下使用哪些工具", "customClaudeCliPathDescription": "手动指定自定义 Claude CLI 可执行文件路径", "apiKeyHelperScript": "apiKey 助手脚本", "userHooks": "用户 Hooks", "cleanupDescription": "本地保留聊天记录的时间（默认：30天）", "claudeCodeInstallationDescription": "选择要使用的 Claude Code 安装版本。推荐使用捆绑版本以获得最佳兼容性。", "advancedSettings": "高级设置", "backToHome": "← 返回主页", "setCustomPath": "设置自定义路径", "claudeCodeInstallation": "Claude Code安装", "customClaudeCliPath": "自定义Claude CLI路径", "newClaudeSession": "新建 Claude Code 会话", "browseClaudeSessions": "浏览您的 Claude Code 会话", "noProjectsFound": "在 ~/.claude/projects 中未找到项目", "ccProjectsTitle": "CC 项目", "loadingProjects": "加载项目失败。请确保 ~/.claude 目录存在。", "loadingSessions": "加载此项目的会话失败。", "claudeStillResponding": "<PERSON> 仍在响应。如果您离开，<PERSON> 将在后台继续运行。\n\n您可以从项目视图返回此会话。\n\n您要继续吗？", "configureClaudePreferences": "配置 Claude Code 偏好设置", "savingSettings": "保存中...", "saveSettings": "保存设置", "about": "关于", "basicInformation": "基本信息", "agentNameRequired": "智能体名称是必需的", "systemPromptRequired": "系统提示词是必需的", "failedToCreateAgent": "创建智能体失败", "failedToUpdateAgent": "更新智能体失败", "unsavedChanges": "您有未保存的更改。点击保存以保持它们。", "editAgent": "编辑 CC 智能体", "createAgent": "创建 CC 智能体", "updateAgent": "更新您的 Claude Code 智能体", "createNewAgent": "创建新的 Claude Code 智能体", "saving": "保存中...", "save": "保存", "agentName": "智能体名称", "agentIcon": "智能体图标", "model": "模型：", "claude4Sonnet": "Claude 4 Sonnet", "claude4Opus": "Claude 4 Opus", "fasterEfficient": "更快，适用于大多数任务", "moreCapable": "更强大，适用于复杂任务", "defaultTaskOptional": "默认任务（可选）", "defaultTaskDescription": "执行智能体时将用作默认任务占位符", "systemPrompt": "系统提示词", "defineAgentBehavior": "定义您的 CC 智能体的行为和能力", "enterUrl": "输入网址...", "preview": "预览", "webPreview": "网页预览", "searchTodos": "搜索待办事项...", "restoreCheckpoint": "恢复到检查点 \"{description}\"？当前状态将保存为新检查点。", "beforeMajorRefactoring": "例如，重大重构之前", "selectTable": "选择表", "searchInTable": "在表中搜索...", "sqlQuery": "SQL 查询", "searchCommands": "搜索命令...", "commandName": "例如，review, fix-issue", "commandCategory": "例如，frontend, backend", "commandDescription": "简要描述此命令的功能", "commandPrompt": "输入提示词内容。使用 $ARGUMENTS 表示动态值。", "pathToClaudeCli": "输入 Claude CLI 可执行文件路径（例如，C:\\path\\to\\claude.exe）", "bashExample": "例如，Bash(npm run test:*)", "exitFullScreen": "退出全屏 (ESC)", "enterFullScreen": "进入全屏", "invalidUrl": "无效的网址", "goBack": "返回", "goForward": "前进", "failedToLoadTimeline": "加载时间线失败", "failedToCreateCheckpoint": "创建检查点失败", "failedToRestoreCheckpoint": "恢复检查点失败", "failedToCompareCheckpoints": "比较检查点失败", "current": "当前", "noPrompt": "无提示词", "loadingTimeline": "正在加载时间线...", "noCheckpointsYet": "尚无检查点", "descriptionOptional": "描述（可选）", "failedToLoadTables": "加载表失败", "failedToLoadTableData": "加载表数据失败", "failedToExecuteSQL": "执行 SQL 失败", "failedToResetDatabase": "重置数据库失败", "null": "空值", "true": "真", "false": "假", "basicInformationSection": "基本信息", "task": "任务", "showSystemInitialization": "显示系统初始化信息", "showSystemInitializationDescription": "在会话开始时显示Session ID、Model、工作目录和可用工具信息", "includeCoAuthoredBy": "包含 \"Co-authored by <PERSON>\"", "includeCoAuthoredByDescription": "在 git 提交和拉取请求中添加 Claude 署名", "verboseOutput": "详细输出", "verboseOutputDescription": "显示完整的 bash 和命令输出", "chatRetentionDays": "聊天记录保留天数", "claudeBinaryPathChanged": "Claude 二进制文件路径已更改。请记住保存您的设置。", "pleaseEnterValidPath": "请输入有效路径", "customClaudeCliPathSetSuccessfully": "自定义 Claude CLI 路径设置成功", "revertedToAutoDetection": "已恢复到自动检测", "failedToSetCustomPath": "设置自定义路径失败", "failedToClearCustomPath": "清除自定义路径失败", "failedToLoadSettings": "加载设置失败。请确保 ~/.claude 目录存在。", "settingsSavedSuccessfully": "设置保存成功！", "failedToSaveSettings": "保存设置失败", "setPath": "设置路径", "revertToAutoDetection": "恢复到自动检测", "customPathValidationDescription": "自定义路径将在保存前验证。确保文件存在且是有效的 Claude CLI 可执行文件。", "permissions": "权限", "environment": "环境", "advanced": "高级", "hooks": "钩子", "commands": "命令", "provider": "代理商", "addRule": "添加规则", "noDenyRulesConfigured": "未配置拒绝规则。", "bashExampleDeny": "例如，Bash(curl:*)", "examples": "示例", "bashAllowAll": "允许所有 bash 命令", "bashAllowExact": "允许特定命令", "bashAllowPrefix": "允许带前缀的命令", "readSpecificFile": "允许读取特定文件", "editDocsDirectory": "允许编辑 docs 目录中的文件", "environmentVariablesDescription": "应用于每个 Claude Code 会话的环境变量", "addVariable": "添加变量", "noEnvironmentVariablesConfigured": "未配置环境变量。", "environmentVariableToggleDescription": "使用开关来启用或禁用环境变量。只有启用的变量会被应用到 Claude Code 会话中。", "enableEnvironmentVariable": "启用环境变量", "disableEnvironmentVariable": "禁用环境变量", "value": "值", "commonVariables": "常用变量：", "enableDisableTelemetry": "启用/禁用遥测 (0 或 1)", "customModelName": "自定义模型名称", "disableCostWarnings": "禁用费用警告 (1)", "advancedSettingsDescription": "面向高级用户的其他配置选项", "apiKeyHelperScriptPath": "/path/to/generate_api_key.sh", "apiKeyHelperDescription": "用于为 API 请求生成认证值的自定义脚本", "rawSettingsJson": "原始设置 (JSON)", "rawSettingsDescription": "这显示了将保存到 ~/.claude/settings.json 的原始 JSON", "providerConfigApplied": "代理商配置已应用", "apiAddress": "API 地址", "authToken": "认证 Token", "apiKey": "API 密钥", "providerConfigNote": "代理商配置已保存到 Raw Settings 的 env 部分，Claude 实例将自动使用这些设置", "noProviderConfigApplied": "未应用代理商配置", "useProviderTabMessage": "使用\"代理商\"标签页来配置 API 代理商设置", "userHooksDescription": "配置适用于您用户帐户的所有 Claude Code 会话的钩子。这些存储在", "userHooksStorageLocation": "~/.claude/settings.json", "loadingExecutionDetails": "加载执行详情失败", "runNotFound": "未找到运行记录", "executionHistory": "执行历史", "stop": "停止", "copyOutput": "复制输出", "copyAsJsonl": "复制为 JSONL", "copyAsMarkdown": "复制为 Markdown", "systemInitialization": "## 系统初始化\n\n", "sessionId": "会话 ID", "workingDirectory": "工作目录", "tools": "工具", "assistant": "助手", "tool": "工具", "tokensInputOutput": "令牌: {{input}} 输入, {{output}} 输出", "user": "用户", "toolResult": "工具结果", "executionResult": "执行结果", "error": "错误", "userStoppedExecutionMessage": "用户停止了执行", "status": "状态", "tokens": "令牌", "cost": "成本", "date": "**日期：**", "checkpointSettings": "检查点设置", "close": "关闭", "experimentalFeature": "实验性功能", "checkpointingCaution": "检查点功能可能影响目录结构或导致数据丢失。请谨慎使用。", "automaticCheckpoints": "自动检查点", "automaticCheckpointsDescription": "根据选定的策略自动创建检查点", "checkpointStrategy": "检查点策略", "manualOnly": "仅手动", "perPrompt": "每次提示后", "perToolUse": "工具使用后", "smartRecommended": "智能（推荐）", "manualOnlyDescription": "检查点将仅手动创建", "perPromptDescription": "每次用户提示后将创建一个检查点", "perToolUseDescription": "每次工具使用后将创建一个检查点", "smartDescription": "将在破坏性操作后创建检查点", "storageManagement": "存储管理", "totalCheckpoints": "总检查点数", "keepRecentCheckpoints": "保留最近的检查点", "cleanUp": "清理", "removeOldCheckpoints": "删除旧检查点，仅保留最近的 {{count}} 个", "loadCheckpointSettingsFailed": "加载检查点设置失败", "settingSavedSuccessfully": "设置保存成功", "saveCheckpointSettingsFailed": "保存检查点设置失败", "deletedOldCheckpoints": "已删除 {{count}} 个旧检查点", "cleanupCheckpointsFailed": "清理检查点失败", "usageDashboard": "使用情况仪表盘", "trackUsageAndCosts": "追踪您的 Claude Code 使用情况和成本", "allTime": "所有时间", "last7Days": "近 7 天", "last30Days": "近 30 天", "today": "今天", "loadingUsageStats": "正在加载使用统计数据...", "loadUsageStatsFailed": "加载使用统计数据失败。请重试。", "retry": "重试", "totalCost": "总成本", "totalSessions": "总会话", "totalTokens": "总令牌", "averagePerSessionCost": "平均每会话成本", "overview": "概览", "byModel": "按模型", "byProject": "按项目", "bySessions": "按会话", "byApiBaseUrl": "按API地址", "timeline": "时间线", "tokenBreakdown": "令牌明细", "inputTokens": "输入令牌", "outputTokens": "输出令牌", "cacheWrites": "缓存写入", "cacheReads": "缓存读取", "mostUsedModels": "最常用模型", "topProjects": "热门项目", "sessions": "个会话", "usageByModel": "按模型使用情况", "input": "输入： ", "output": "输出： ", "cacheWrite": "缓存写： ", "cacheRead": "缓存读： ", "usageByProject": "按项目使用情况", "perSession": "/会话", "usageBySessions": "按会话使用情况", "usageByApiBaseUrl": "按API地址使用情况", "dailyUsage": "日常使用情况", "costLabel": "成本：", "modelsUsed": "个模型", "dailyUsageOverTime": "日常使用情况随时间变化", "noUsageDataForPeriod": "所选时期内无使用数据", "usingTool": "使用工具：", "editResult": "编辑结果", "multiEditResult": "多重编辑结果", "directoryContents": "目录内容", "readResult": "读取结果", "toolDidNotReturnOutput": "工具未返回任何输出", "executionFailed": "执行失败", "errorRenderingMessage": "渲染消息错误", "unknownError": "未知错误", "tokensLabel": "令牌：", "totalTokensStats": "总令牌：{{total}} ({{input}} 输入, {{output}} 输出)", "durationLabel": "持续时间：", "turnsLabel": "轮次：", "ccAgentsTitle": "CC 智能体", "manageCCAgents": "管理您的 Claude Code 智能体", "import": "导入", "fromFile": "从文件", "fromGitHub": "从 GitHub", "createCCAgent": "创建 CC 智能体", "noAgentsYet": "还没有智能体", "createFirstAgent": "创建您的第一个 CC 智能体开始使用", "createdLabel": "创建时间：", "executeAgent": "执行智能体", "executeAgentTitle": "执行智能体", "editAgentTitle": "编辑智能体", "exportAgentTitle": "导出智能体到 .claudia.json", "deleteAgent": "删除智能体", "deleteAgentTitle": "删除智能体", "previous": "上一页", "next": "下一页", "pageOf": "第 {{current}} 页，共 {{total}} 页", "recentExecutions": "最近执行", "deleteAgentDialog": "删除智能体", "deleteAgentConfirm": "您确定要删除智能体 \"{name}\" 吗？此操作无法撤消，将永久删除智能体及其所有关联数据。", "deleting": "删除中...", "deleteAgentButton": "删除智能体", "agentExportSuccess": "智能体 \"{name}\" 导出成功", "agentExportFailed": "导出智能体失败", "agentImportSuccess": "智能体导入成功", "agentImportFailed": "导入智能体失败", "importFromGitHubSuccess": "从 GitHub 成功导入智能体", "providerName": "代理商名称", "providerDescription": "描述", "authenticationInfo": "认证信息", "atLeastOneRequired": "(至少填写一个)", "modelName": "模型名称", "someProvidersRequireModel": "部分代理商需要指定特定的模型名称", "addingConfig": "添加中...", "updatingConfig": "更新中...", "addConfig": "添加配置", "updateConfig": "更新配置", "pleaseEnterProviderName": "请输入代理商名称", "pleaseEnterApiAddress": "请输入API地址", "apiAddressMustStartWith": "API地址必须以 http:// 或 https:// 开头", "pleaseEnterAuthTokenOrApiKey": "请至少填写认证Token或API Key中的一个", "addOrUpdateProviderFailed": "{action}代理商配置失败: {error}", "updating": "更新", "adding": "添加", "providerManager": "代理商管理", "switchClaudeApiProviders": "一键切换不同的 Claude API 代理商", "addProvider": "添加代理商", "viewCurrentConfig": "查看当前配置", "clearConfig": "清理配置", "clearing": "清理中...", "noProvidersConfigured": "还没有配置任何代理商", "addFirstProvider": "添加第一个代理商", "currentlyUsed": "当前使用", "description": "描述：", "apiAddressLabel": "API地址：", "authTokenLabel": "认证Token：", "apiKeyLabel": "API Key：", "modelLabel": "- 模型：", "testConnection": "测试连接", "editProvider": "编辑代理商", "deleteProvider": "删除代理商", "selected": "已选择", "switchToConfig": "切换到此配置", "show": "显示", "hide": "隐藏", "currentEnvironmentConfig": "当前环境变量配置", "noAnthropicEnvVars": "未检测到任何 ANTHROPIC 环境变量", "editProviderDialog": "编辑代理商", "addProviderDialog": "添加代理商", "loadProviderConfigFailed": "加载代理商配置失败", "switchProviderFailed": "切换代理商失败", "clearConfigFailed": "清理配置失败", "connectionTestFailed": "连接测试失败", "confirmDeleteProvider": "确定要删除代理商 \"{name}\" 吗？", "providerDeleteSuccess": "代理商删除成功", "deleteProviderFailed": "删除代理商失败", "providerUpdateSuccess": "代理商更新成功", "providerAddSuccess": "代理商添加成功", "updateProviderFailed": "更新代理商失败", "addProviderFailed": "添加代理商失败", "loadingProviderConfig": "正在加载代理商配置...", "basicInfo": "基本信息", "providerNameRequired": "代理商名称 *", "providerNamePlaceholder": "例如：OpenAI 官方", "descriptionPlaceholder": "例如：OpenAI 官方 API", "apiAddressRequired": "API 地址 *", "apiAddressPlaceholder": "https://api.anthropic.com", "authTokenPlaceholder": "sk-ant-...", "apiKeyPlaceholder": "sk-...", "modelNamePlaceholder": "claude-3-5-sonnet-20241022 (可选)", "hooksConfiguration": "Hooks 配置", "preToolUse": "工具使用前", "preToolUseDescription": "在工具调用前运行，可以阻止并提供反馈", "postToolUse": "工具使用后", "postToolUseDescription": "在工具成功完成后运行", "notification": "通知", "notificationDescription": "当 Claude 需要注意时自定义通知", "stopDescription": "当 Claude 完成响应时运行", "subagentStop": "子智能体停止", "subagentStopDescription": "当 Claude 子智能体（任务）完成时运行", "loadingHooksConfig": "正在加载 Hooks 配置...", "projectScope": "项目范围", "localScope": "本地范围", "userScope": "用户范围", "templates": "模板", "configureShellCommands": "配置在 Claude Code 生命周期各个时间点执行的 Shell 命令。", "localSettingsNotCommitted": "这些设置不会提交到版本控制。", "validationErrors": "验证错误：", "securityWarnings": "安全警告：", "addCommand": "添加命令", "noCommandsAdded": "还没有添加命令", "enterCommandPlaceholder": "输入命令行命令...", "enterShellCommandPlaceholder": "Enter shell command...", "secondsLabel": "秒", "pattern": "模式", "toolNamePattern": "工具名称模式（支持正则表达式）。留空匹配所有工具。", "patternPlaceholder": "例如：<PERSON><PERSON>, Edit|Write, mcp__.*", "customPattern": "自定义", "commonPatterns": "常用模式", "noHooksConfigured": "此事件未配置钩子", "addHook": "添加钩子", "addAnother": "添加另一个", "matcher": "匹配器", "command": "命令", "hookTemplates": "钩子模板", "chooseTemplate": "选择预配置的钩子模板以快速开始", "matcherLabel": "匹配器：", "selectClaudeInstallation": "选择 Claude Code 安装", "searchingInstallations": "搜索 Claude 安装中...", "multipleInstallationsFound": "在您的系统上找到多个 Claude Code 安装。请选择您想要使用的安装。", "claudeNotFoundInCommonLocations": "在常见安装位置未找到 Claude Code。请安装 Claude Code 继续。", "searchedLocations": "已搜索位置：", "installTip": "您可以使用以下方式安装 Claude Code", "installationGuide": "安装指南", "validating": "验证中...", "saveSelection": "保存选择", "noInstallationFound": "未找到安装", "pleaseSelectClaudeInstallation": "请选择一个 Claude 安装", "claudeCodeInstallationSelector": "Claude Code 安装选择器", "loadingAvailableInstallations": "加载可用安装中...", "errorLoadingInstallations": "加载安装时错误", "availableInstallations": "可用安装", "selectClaudeInstallationPlaceholder": "选择 Claude 安装", "bundled": "打包版", "claudeCodeBundled": "<PERSON>（打包版）", "versionUnknown": "版本未知", "recommended": "推荐", "systemInstallations": "系统安装", "system": "系统", "customInstallations": "自定义安装", "custom": "自定义", "selectedInstallation": "选定的安装", "path": "路径：", "source": "来源：", "version": "版本：", "chooseBestCompatibility": "选择您首选的 Claude Code 安装。推荐使用打包版以获得最佳兼容性。", "savingSelection": "保存中...", "selectProjectDirectory": "选择项目目录", "projectDirectory": "项目目录", "projectDirectoryPlaceholder": "/path/to/your/project", "projectNotSelected": "未选择项目", "backToSessionList": "返回会话列表", "claudeCodeSession": "<PERSON> 代码会话", "timelineNavigator": "时间线导航", "sessionTimeline": "会话时间线", "queuedPrompts": "队列中的提示（{{count}}）", "scrollToTop": "滚动到顶部", "scrollToBottom": "滚动到底部", "forkSession": "分叉会话", "createSessionBranch": "从选定的检查点创建新的会话分支。", "newSessionName": "新会话名称", "newSessionNamePlaceholder": "例如，替代方法", "createFork": "创建分叉", "slashCommands": "斜杠命令", "manageProjectSlashCommands": "Manage project-specific slash commands for {projectPath}", "pleaseSelectProjectDirectory": "请先选择项目目录", "sendPromptFailed": "发送提示失败", "selectDirectoryFailed": "选择目录失败：{error}", "loadingSessionHistory": "加载会话历史记录...", "failedToLoadSessionHistory": "加载会话历史记录失败", "initializingClaudeCode": "初始化 Claude Code...", "claudeCodeMarkdownSession": "# <PERSON> 代码会话\n\n", "project": "项目", "sessionIdLabel": "- 会话 ID：", "workingDirectoryLabel": "- 工作目录：", "toolsLabel": "- 工具：", "assistantLabel": "## 助手\n\n", "toolLabel": "### 工具：", "tokensMarkdown": "*令牌： {{input}} 输入， {{output}} 输出*\n\n", "userLabel": "## 用户\n\n", "toolResultLabel": "### 工具结果\n\n", "executionResultLabel": "## 执行结果\n\n", "errorLabel": "**错误：**", "sessionCancelledByUser": "会话已被用户取消", "failedToCancelExecution": "取消执行失败：{error}。进程可能仍在后台运行。", "forkToNewSession": "分叉-", "failedToForkCheckpoint": "分叉检查点失败", "forkedToNewSession": "已分叉到新会话：", "loadCommandsFailed": "加载命令失败", "saveCommandFailed": "保存命令失败", "deleteCommandFailed": "删除命令失败", "projectSlashCommands": "项目斜杠命令", "createCustomCommandsForProject": "为此项目创建自定义命令", "createCustomCommandsWorkflow": "创建自定义命令以简化您的工作流程", "newCommand": "新建命令", "allCommands": "所有命令", "arguments": "参数", "toolsCount": "{{count}} 个工具", "script": "脚本", "file": "文件", "hideContent": "隐藏内容", "showContent": "显示内容", "commandsNotFound": "未找到命令", "noProjectCommandsYet": "尚未创建项目命令", "noCommandsYet": "尚未创建命令", "createFirstProjectCommand": "创建您的第一个项目命令", "createFirstCommand": "创建您的第一个命令", "editCommand": "编辑命令", "createNewCommand": "创建新命令", "scope": "作用域", "userGlobal": "用户（全局）", "availableInAllProjects": "在所有项目中可用", "availableOnlyInThisProject": "仅在此项目中可用", "commandNameRequired": "命令名称*", "commandNamespace": "命令空间（可选）", "commandContentRequired": "命令内容*", "argumentsUsage": "使用 <code>$ARGUMENTS</code> 表示用户输入，<code>@filename</code> 表示文件，使用 <code>!`command`</code> 表示 bash 命令", "allowedTools": "允许的工具", "selectToolsForClaude": "选择 Claude 可以与此命令一起使用的工具", "argumentsPlaceholder": "[参数]", "deleteCommand": "删除命令", "confirmDeleteCommand": "您确定要删除此命令吗？", "actionCannotBeUndone": "此操作无法撤销。命令文件将被永久删除。", "projectCommands": "项目命令", "userCommands": "用户命令", "databaseStorage": "数据库存储", "resetDb": "重置数据库", "rowsLabel": "行", "addNewRow": "新增行", "actions": "操作", "showingRows": "显示 {{start}} 到 {{end}}，共 {{total}} 行", "editRow": "编辑行", "updateRowValues": "更新 {table} 表中此行的值。", "primaryKey": "（主键）", "type": "类型：", "notNull": "，非空", "default": "，默认值：", "update": "更新", "newRow": "新行", "addNewRowToTable": "向 {table} 表添加新行。", "required": "（必填）", "insert": "插入", "deleteRow": "删除行", "confirmDeleteRow": "您确定要删除此行吗？此操作无法撤销。", "resetDatabase": "重置数据库", "resetDatabaseWarning": "这将删除所有数据并使用默认结构重新创建数据库（agents、agent_runs 和 app_settings 的空表）。数据库将恢复到您初次安装应用程序时的状态。此操作无法撤销。", "allDataWillBeDeleted": "您的所有智能体、运行记录和设置将被永久删除！", "sqlQueryEditor": "SQL 查询编辑器", "executeSqlCaution": "在数据库上执行原始 SQL 查询。请谨慎使用。", "sqlQueryLabel": "SQL 查询", "queryExecutedSuccessfully": "查询执行成功。影响了 {affected} 行。", "lastInsertId": "最后插入 ID：{id}", "execute": "执行", "databaseResetComplete": "数据库重置完成：数据库已恢复到默认状态，具有空表（agents、agent_runs、app_settings）。", "updateRowFailed": "更新行失败", "deleteRowFailed": "删除行失败", "insertRowFailed": "插入行失败", "mcpServers": "MCP 服务器", "manageMcpServers": "管理模型上下文协议服务器", "loadMcpServersFailed": "加载 MCP 服务器失败。请确保 Claude Code 已安装。", "mcpServerAddedSuccess": "MCP 服务器添加成功！", "serverDeletedSuccess": "服务器 \"{name}\" 删除成功！", "serversImportedSuccess": "成功导入 {imported} 个服务器！", "serversImportedWithFailures": "导入 {imported} 个服务器，{failed} 个失败", "servers": "服务器", "addServer": "添加服务器", "importExport": "导入/导出", "composePrompt": "编写您的提示词", "attachmentPreview": "附件预览", "screenshotPreview": "截图预览", "promptInputPlaceholder": "在这里输入您的提示词...", "thinking": "思考：", "dropImagesHere": "在此放置图片...", "askClaudeAnything": "问 Claude 任何事情...", "keyboardShortcuts": "按 Enter 发送，Shift+Enter 换行，或粘贴图片", "keyboardShortcutsWithFiles": "按 Enter 发送，Shift+Enter 换行，@ 提及文件，/ 输入命令，或粘贴图片", "saveClipboardImageFailed": "保存剪贴板图片失败，请重试", "pasteImageFailed": "粘贴图片失败，请重试", "letClaudeDecide": "让 Claude 自己决定", "basicReasoning": "基本推理", "deeperThinking": "更深入分析", "extensiveReasoning": "广泛推理", "maximumComputation": "最大计算", "moreCapableBetter": "更强大，更适合复杂任务"}, "relayStations": {"title": "中转站管理", "description": "管理你的 Claude API 中转站", "addStation": "添加中转站", "stationList": "中转站列表", "noStations": "暂无中转站", "noStationsDescription": "点击'添加中转站'开始配置", "stationName": "站点名称", "stationDescription": "描述", "apiUrl": "API地址", "adapterType": "适配器类型", "authMethod": "认证方式", "systemToken": "系统令牌", "enabled": "启用", "disabled": "禁用", "status": "状态", "details": "详情", "tokens": "Token", "logs": "日志", "delete": "删除", "edit": "编辑", "testConnection": "测试连接", "connectionSuccess": "连接成功", "connectionFailed": "连接失败", "responseTime": "响应时间", "stationInfo": "站点信息", "version": "版本", "announcement": "公告", "tokenManagement": "Token 管理", "tokenList": "Token 列表", "userInfo": "用户信息", "userId": "用户ID", "username": "用户名", "email": "邮箱", "balance": "余额", "used": "已用", "requestCount": "请求量", "noTokens": "暂无 Token", "selectTokenForUserInfo": "选择一个 Token 查看用户信息", "cannotGetUserInfo": "无法获取用户信息", "cannotGetStationInfo": "无法获取站点信息", "noLogs": "暂无日志", "selectStationForLogs": "选择一个中转站查看日志", "confirmDelete": "确定要删除中转站 \"{name}\" 吗？", "addStationSuccess": "中转站添加成功", "updateStationSuccess": "中转站更新成功", "deleteStationSuccess": "中转站删除成功", "fetchStationsFailed": "获取中转站列表失败", "deleteStationFailed": "删除中转站失败", "addStationDialog": {"title": "添加中转站", "description": "添加一个新的中转站配置"}, "stationDetailsDialog": {"title": "中转站详情", "description": "中转站详细信息和状态"}, "tokenManagementDialog": {"title": "Token 管理", "description": "管理中转站的访问令牌和用户信息"}, "adapters": {"newapi": "NewAPI", "oneapi": "OneAPI", "yourapi": "YourAPI", "custom": "自定义"}, "authMethods": {"bearerToken": "<PERSON><PERSON>", "apiKey": "API Key", "custom": "自定义"}}}
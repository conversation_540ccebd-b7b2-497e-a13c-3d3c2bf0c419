# UI 修复测试指南

## 测试环境
- 开发服务器：http://localhost:1420/
- 浏览器：建议使用 Chrome、Firefox、Safari 进行测试
- 设备：桌面、平板、手机

## 测试步骤

### 1. 浅色主题显示测试

#### 步骤：
1. 打开应用 http://localhost:1420/
2. 切换到浅色主题（如果有主题切换按钮）
3. 导航到 Claude 代码会话页面

#### 检查项目：
- [ ] 文字在浅色背景上清晰可读
- [ ] 背景对比度适中，不刺眼
- [ ] 卡片和弹出框有足够的对比度
- [ ] 边框清晰可见
- [ ] 按钮和交互元素突出显示
- [ ] 透明背景不会导致内容重叠

#### 预期结果：
- 所有文字都应该有足够的对比度（至少 4.5:1）
- 界面元素层次分明
- 没有内容重叠或难以阅读的区域

### 2. 对话区域与输入区域布局测试

#### 步骤：
1. 进入 Claude 代码会话页面
2. 输入一些测试消息
3. 发送多条消息以创建长对话
4. 测试滚动行为

#### 检查项目：
- [ ] 对话内容完全可见，不被输入框遮挡
- [ ] 输入框固定在底部，不覆盖对话内容
- [ ] 滚动到底部时，最新消息完全可见
- [ ] 导航箭头位置正确，不被输入框遮挡
- [ ] Token 计数器位置合适
- [ ] 排队提示（如果有）位置正确

#### 预期结果：
- 对话内容和输入区域完全分离
- 滚动行为流畅，无内容丢失
- 所有浮动元素位置正确

### 3. 响应式布局测试

#### 桌面测试（1920x1080）：
- [ ] 布局宽敞，元素间距合理
- [ ] 输入区域不会过宽
- [ ] 导航元素位置合适

#### 平板测试（768x1024）：
- [ ] 布局自适应屏幕宽度
- [ ] 输入区域大小合适
- [ ] 浮动元素位置调整正确

#### 手机测试（375x667）：
- [ ] 输入区域占用合理空间
- [ ] 导航箭头位置适合触摸
- [ ] 安全区域适配正确
- [ ] 虚拟键盘弹出时布局正常

### 4. 交互测试

#### 输入区域测试：
- [ ] 点击输入框正常聚焦
- [ ] 输入文字流畅
- [ ] 发送按钮响应正常
- [ ] 展开/收缩功能正常

#### 浮动元素测试：
- [ ] 导航箭头点击正常
- [ ] Token 计数器显示正确
- [ ] 排队提示交互正常
- [ ] 所有按钮可点击且响应

### 5. 主题切换测试

#### 步骤：
1. 在浅色主题下完成上述测试
2. 切换到深色主题
3. 重复所有测试项目

#### 检查项目：
- [ ] 主题切换平滑无闪烁
- [ ] 所有元素正确应用新主题
- [ ] 对比度在两个主题下都合适
- [ ] 透明效果在两个主题下都正常

### 6. 性能测试

#### 检查项目：
- [ ] 页面加载速度正常
- [ ] 滚动性能流畅
- [ ] 主题切换无延迟
- [ ] 输入响应及时

## 常见问题排查

### 如果发现问题：

1. **文字对比度不足**：
   - 检查 CSS 变量是否正确应用
   - 验证浏览器是否支持 OKLCH 颜色空间

2. **布局重叠**：
   - 检查 z-index 层级
   - 验证 padding 和 margin 计算
   - 确认 `env(safe-area-inset-bottom)` 支持

3. **响应式问题**：
   - 检查媒体查询是否生效
   - 验证 CSS 变量在不同屏幕下的表现

4. **主题切换问题**：
   - 检查 ThemeContext 是否正常工作
   - 验证 CSS 类是否正确切换

## 浏览器兼容性

### 支持的浏览器：
- Chrome 88+
- Firefox 113+
- Safari 15.4+
- Edge 88+

### 注意事项：
- OKLCH 颜色空间需要较新的浏览器支持
- `env(safe-area-inset-bottom)` 主要在移动设备上生效
- `backdrop-filter` 需要浏览器支持

## 报告问题

如果发现任何问题，请记录：
1. 浏览器版本和设备信息
2. 具体的重现步骤
3. 预期行为 vs 实际行为
4. 截图或录屏（如果可能）

## 验收标准

所有测试项目都应该通过，特别是：
1. 浅色主题下的可读性
2. 对话区域和输入区域的完全分离
3. 响应式布局的正确性
4. 跨浏览器的一致性

{"$schema": "https://schema.tauri.app/config/2", "productName": "<PERSON>", "version": "1.0.0", "identifier": "claude.workbench.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "<PERSON>", "width": 800, "height": 600}], "security": {"csp": "default-src 'self'; img-src 'self' asset: https://asset.localhost blob: data:; style-src 'self' 'unsafe-inline'; script-src 'self' 'unsafe-eval'; connect-src 'self' ipc: https://ipc.localhost", "assetProtocol": {"enable": true, "scope": ["**"]}}}, "plugins": {"fs": {"scope": ["$HOME/**", "$TEMP/**", "$TMP/**"], "allow": ["readFile", "writeFile", "readDir", "copyFile", "createDir", "removeDir", "removeFile", "renameFile", "exists"]}, "shell": {"open": true}}, "bundle": {"active": true, "targets": ["msi", "nsis"], "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.ico", "icons/icon.png"], "resources": [], "externalBin": [], "copyright": "", "category": "DeveloperTool", "shortDescription": "Professional GUI Toolkit for Claude CLI (Windows Only)", "longDescription": "Claude Suite is a professional desktop application and comprehensive toolkit for Claude CLI, designed specifically for Windows users. Provides an intuitive interface for AI-powered development workflows, project management, and advanced Claude interactions."}}
[package]
name = "claude-suite"
version = "1.0.0"
description = "Claude Suite - Professional desktop application and toolkit for Claude CLI"
authors = ["mufeedvh", "123vviekr"]
license = "AGPL-3.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
name = "claude_suite_lib"
crate-type = ["lib", "cdylib", "staticlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = ["protocol-asset", "tray-icon", "image-png"] }
tauri-plugin-shell = "2"
tauri-plugin-dialog = "2"
tauri-plugin-fs = "2"
tauri-plugin-process = "2"
tauri-plugin-updater = "2"
tauri-plugin-notification = "2"
tauri-plugin-clipboard-manager = "2"
tauri-plugin-global-shortcut = "2"
tauri-plugin-http = "2"
image = "0.25"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
tokio = { version = "1", features = ["full"] }
rusqlite = { version = "0.32", features = ["bundled"] }
dirs = "5"
chrono = { version = "0.4", features = ["serde"] }
anyhow = "1"
log = "0.4"
env_logger = "0.11"
regex = "1"
glob = "0.3"
base64 = "0.22"
reqwest = { version = "0.12", features = ["json"] }
futures = "0.3"
async-trait = "0.1"
tempfile = "3"
sha2 = "0.10"
zstd = "0.13"
uuid = { version = "1.6", features = ["v4", "serde"] }
walkdir = "2"
serde_yaml = "0.9"
once_cell = "1.19"
urlencoding = "2.1"


[profile.release]
# Enable more aggressive optimizations for smaller binary size
opt-level = "z"     # Optimize for size
lto = true          # Enable Link Time Optimization
codegen-units = 1   # Reduce parallel codegen units
panic = "abort"     # Abort on panic instead of unwinding
strip = true        # Strip symbols from binary
overflow-checks = false  # Disable overflow checks in release mode for smaller size
debug = false       # Disable debug info
incremental = false # Disable incremental compilation for better optimization

# Additional size optimizations
[profile.release.package."*"]
opt-level = "z"     # Optimize all dependencies for size

[features]
# This feature is used for production builds or when a dev server is not specified, DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]

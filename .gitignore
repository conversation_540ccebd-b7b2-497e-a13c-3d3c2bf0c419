# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local
*.bun-build

# Tauri binaries (built executables)
src-tauri/binaries/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
temp_lib/

.cursor/
AGENTS.md
*_TASK.md

# Claude project-specific files
.claude/

# Provider configurations (contains sensitive API keys)
providers.json
hidden_projects.json

# Build artifacts
src-tauri/target/
src-tauri/Cargo.lock

# Temporary files
nul

# Environment files
.env
.env.local
.env.*.local

# Bundle outputs
*.msi
*.exe
*.dmg
*.pkg
*.deb
*.rpm
*.appimage

# Test and coverage
coverage/
test-results/
playwright-report/
playwright/.cache/
